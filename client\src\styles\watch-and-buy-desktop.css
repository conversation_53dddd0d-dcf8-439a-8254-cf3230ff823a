/* Custom styles for the watch-and-buy-desktop component */

/* Video card styling */
.video-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Staggered animation for video cards */
.video-card-container {
  transition: transform 0.7s cubic-bezier(0.34, 1.56, 0.64, 1), opacity 0.6s ease;
}

.video-card-container.leaving-left {
  transform: translateX(-100%) scale(0.9) !important;
  opacity: 0 !important;
  z-index: 5 !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.video-card-container.leaving-right {
  transform: translateX(100%) scale(0.9) !important;
  opacity: 0 !important;
  z-index: 5 !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.video-card-container.entering {
  transition-delay: 0.25s;
  transition-timing-function: cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Video stacker container */
.video-stacker-container {
  margin: 0 auto;
  overflow: visible !important;
  position: relative;
  perspective: 1000px !important;
}

/* Video stack wrapper */
.video-stack-wrapper {
  padding: 0;
  overflow: visible !important;
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d !important;
}

/* Ensure all video cards in the stack are visible */
.video-card-container {
  position: absolute;
  display: block !important;
  visibility: visible !important;
  transform-origin: center center;
  backface-visibility: hidden;
  will-change: transform, opacity;
}

/* Timeline dots */
.timeline-dot {
  transition: width 0.3s ease, background-color 0.3s ease;
}

.timeline-dot:hover {
  background-color: rgba(255, 255, 255, 0.8) !important;
}

/* Navigation arrows */
.nav-arrow {
  background: transparent;
  border: none;
  outline: none;
  cursor: pointer;
  padding: 0;
  transition: transform 0.3s ease;
}


.nav-arrow:hover .big-arrow-left,
.nav-arrow:hover .big-arrow-right {
  background: rgba(0, 0, 0, 0.7);
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.5),
    0 6px 12px rgba(0, 0, 0, 0.4);
}

/* Bottom info bar */
.info-bar {
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.info-bar:hover {
  transform: translateY(-2px);
}

/* Action buttons */
.action-button {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* React player container */
.react-player {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover;
}

/* Prevent context menu and selection on video player */
.react-player-container {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  pointer-events: auto;
}

.react-player-container video {
  pointer-events: auto;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
}

/* Make sure videos are visible in the slider */
.watch-buy-slider video {
  object-fit: cover;
  width: 100% !important;
  height: 100% !important;
  display: block;
}

/* Video controls - Completely redesigned for maximum clickability */
.video-controls {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  gap: 8px;
  z-index: 9999;
  opacity: 1;
  pointer-events: auto;
}

/* Make the control buttons and icons fully clickable */
.video-controls svg {
  width: 100%;
  height: 100%;
  padding: 8px;
  cursor: pointer;
  pointer-events: auto !important;
  transition: all 0.2s ease;
}

.video-controls svg:hover {
  transform: scale(1.1);
}

.video-controls svg:active {
  transform: scale(0.9);
}

/* Enhanced Video Progress Bar - White timeline moved down */
.video-progress-container {
  height: 6px !important;
  background-color: rgba(0, 0, 0, 0.7) !important;
  z-index: 9999 !important;
  position: fixed !important;
  top: 12px !important; /* Moved down from the very top */
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  pointer-events: none;
  border-top: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.video-progress-bar {
  height: 100% !important;
  background-color: white !important;
  box-shadow: 0 0 12px rgba(255, 255, 255, 0.9) !important;
}

/* Slider customization */
.watch-buy-slider .slick-track {
  display: flex;
  margin-left: 0;
  margin-right: 0;
}

.watch-buy-slider .slick-slide {
  margin: 0;
  padding: 0 8px; /* Use padding instead of margin for consistent sizing */
  height: auto;
  box-sizing: border-box; /* Ensure padding is included in width calculation */
  width: 20%; /* Exactly 5 cards (100% / 5) */
}

/* Ensure the slider container has proper width */
.watch-buy-slider {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden; /* Prevent horizontal scrolling */
}

/* Slider container */
.slider-container {
  padding: 0 8px;
  box-sizing: border-box;
}

/* Fix for exact 5 cards display */
.watch-buy-slider .slick-list {
  overflow: hidden;
  margin: 0;
}

/* Ensure each slide has equal width */
.watch-buy-slider .slick-slide > div {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
}

/* Big arrow navigation buttons with three sides rounded */
.custom-nav-arrow {
  background: transparent;
  border: none;
  outline: none;
  cursor: pointer;
  padding: 0;
  transition: transform 0.3s ease;
}

.custom-nav-arrow:hover {
  transform: translateY(-50%) scale(1.1) !important;
}

.big-arrow-left,
.big-arrow-right {
  width: 60px;
  height: 60px;
  background: rgba(0, 0, 0, 0.5); /* Slightly darker background */
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.5),
    0 4px 8px rgba(0, 0, 0, 0.3); /* Added outer shadow for depth */
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1); /* Subtle border */
}

.custom-nav-arrow:hover .big-arrow-left,
.custom-nav-arrow:hover .big-arrow-right {
  background: rgba(0, 0, 0, 0.7);
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.5),
    0 6px 12px rgba(0, 0, 0, 0.4);
}

/* Left arrow: round right side */
.big-arrow-left {
  border-radius: 30px; /* semi-circle on the right */
}

/* Right arrow: round left side */
.big-arrow-right {
  border-radius: 30px; /* semi-circle on the left */
}

/* Responsive sizing for arrows and controls */
@media (min-width: 1440px) {
  .big-arrow-left,
  .big-arrow-right {
    width: 70px;
    height: 70px;
  }

  .big-arrow-left svg,
  .big-arrow-right svg {
    width: 32px;
    height: 32px;
  }

  /* Video controls for larger screens */
  .video-control-button {
    width: 50px !important;
    height: 50px !important;
  }

  /* Product info text for larger screens */
  .product-info-title {
    font-size: 1.25rem !important;
  }

  .product-info-price {
    font-size: 1.25rem !important;
  }
}

/* Extra large screens */
@media (min-width: 2000px) {
  .big-arrow-left,
  .big-arrow-right {
    width: 90px;
    height: 90px;
  }

  .big-arrow-left svg,
  .big-arrow-right svg {
    width: 40px;
    height: 40px;
  }

  /* Video controls for extra large screens */
  .video-control-button {
    width: 60px !important;
    height: 60px !important;
  }

  /* Product info text for extra large screens */
  .product-info-title {
    font-size: 1.5rem !important;
  }

  .product-info-price {
    font-size: 1.5rem !important;
  }

  /* Control buttons in modal view */
  .modal-control-button {
    width: 50px !important;
    height: 50px !important;
  }
}


.watch-buy-slider .slick-dots {
  bottom: -30px;
  font-size: 14px !important;
  position: absolute;
  display: block !important;
  width: 100%;
  padding: 5px 0;
  margin: 0;
  list-style: none;
  text-align: center;
  z-index: 20;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 20px;
}

.watch-buy-slider .slick-dots li {
  position: relative;
  display: inline-block;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.watch-buy-slider .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  width: 20px;
  height: 20px;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.watch-buy-slider .slick-dots li button:before {
  font-size: 14px !important;
  line-height: 20px;
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  content: '•';
  text-align: center;
  opacity: .25;
  color: black;
}

.watch-buy-slider .slick-dots li.slick-active button:before {
  opacity: 1;
  color: black;
  transform: scale(1.5);
}



/* Modal view - no padding */
/* Padding already removed from all video-stack-wrapper elements */

/* Ensure 3D stacking works properly */
.modal-view {
  perspective: 1000px;
}

.modal-view .video-stacker-container {
  transform-style: preserve-3d;
}

.modal-view .video-stack-wrapper {
  transform-style: preserve-3d;
}

.modal-view .video-card-container {
  transform-style: preserve-3d;
  backface-visibility: hidden;
}
