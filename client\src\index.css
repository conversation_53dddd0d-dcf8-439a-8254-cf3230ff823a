@tailwind base;
@tailwind components;
@tailwind utilities;

::-webkit-scrollbar {
  display: none;
}

.table-scroll::-webkit-scrollbar {
  display: block;
  height: 6px; /* Adjust scrollbar thickness */
}

.table-scroll::-webkit-scrollbar-thumb {
  background: #888; /* Scrollbar color */
  border-radius: 3px;
}

.table-scroll::-webkit-scrollbar-thumb:hover {
  background: #555; /* Darker color on hover */
}


body {
 font-family: 'Lato', serif;
}
@layer base {
  :root {
    --background: #fed1d6;
    /* --background: #f5ced2; */
    /* --foreground: #2c3315; */
    --foreground: #000000;
    --playground: #ffffff;
    --card: #ffffff;
    --card-foreground: #333333;
    --primary: #2c3315;
    --primary-foreground: #f0f0f0;
    --secondary: #2b2b2b;
    --secondary-foreground: #e0e0e0;
    /* --muted: #e9768a; */
    --muted:#000000;
    --muted-foreground:#2c3315;
    --accent:#272d12;
    --accent-foreground: #f0f0f0;
    --destructive: #e53935;
    --destructive-foreground: #ffffff;
    --border: #bdbdbd;
    --input: #bdbdbd;
    --ring: #333333;
    --radius: 0.5rem;
    --chart-1: #f44336;
    --chart-2: #2196f3;
    --chart-3: #4caf50;
    --chart-4: #ff9800;
    --chart-5: #9c27b0;
  }

  .dark {
    --background: #222222;
    --foreground: #f0f0f0;
    --playground: #fed1d6;
    --card: #333333;
    --card-foreground: #f0f0f0;
    --primary: #333333;
    --primary-foreground: #f0f0f0;
    --secondary: #444444;
    --secondary-foreground: #f0f0f0;
    --muted: #555555;
    --muted-foreground: #cccccc;
    --accent: #666666;
    --accent-foreground: #f0f0f0;
    --destructive: #d32f2f;
    --destructive-foreground: #ffffff;
    --border: #444444;
    --input: #444444;
    --ring: #ffffff;
    --chart-1: #f44336;
    --chart-2: #2196f3;
  }
}


@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply text-foreground;
  }
}

@layer utilities {
  .glow-effect {
    box-shadow: 0 0 10px 2px rgba(211, 47, 47, 0.5),
                0 0 20px 4px rgba(211, 47, 47, 0.3),
                0 0 30px 6px rgba(211, 47, 47, 0.2);
    transition: box-shadow 0.3s ease-in-out;
  }

  .glow-effect:hover {
    box-shadow: 0 0 15px 4px rgba(211, 47, 47, 0.7),
                0 0 30px 8px rgba(211, 47, 47, 0.5),
                0 0 45px 12px rgba(211, 47, 47, 0.3);
  }
}

/* Custom Scrollbar Styles */
.custom-scrollbar {
  height: 80vh;
  overflow-y: auto;
}

/* Prevent download option on videos */
video {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}

/* Prevent context menu on video elements */
video::-webkit-media-controls-enclosure,
video::-webkit-media-controls-panel,
video::-webkit-media-controls {
  -webkit-appearance: none;
  appearance: none;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 12px;
  display: block;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #555;
}




.bg-auth-bg {
  background-image: url('./assets/login-2.png');
  background-size: cover;
  background-position: top;
}


.animate-quote-in {
  animation: quote-in 1s ease-out forwards;
  animation-delay: 0.5s; /* adjust the delay to your liking */
}

.animate-quote-out {
  animation: none;
}

@keyframes quote-in {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes quote-out {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-100%);
    opacity: 0;
  }
}

.slick-slide>div{
    margin: 10px;
}
.slick-icons {
  position: absolute;
  width: 4rem;
  height: 4rem;
  border: 1px solid var(--foreground);
  border-radius: 50%;
  font-size: 1.2rem;
  display: flex;
  justify-content: center;
  align-items: center;

}

.slick-icons:hover {
  cursor: pointer;
  background-color: var(--destructive-foreground);
}

.slick-icons--left,
.slick-icons--right {
  font-size: 2rem;
}

.slick-icons--left {
 top: -5rem;
  right: 5rem;
}

.slick-icons--right {
  top: -5rem;
  right: 0rem;
}

.slick-dots {
  bottom: -30px;
  font-size: 14px !important;
}

.slick-dots li button:before {
  font-size: 14px !important;
}


@media screen and (max-width: 768px) {
  .slick-icons--left {
    top: -4rem;
    right: 5rem;
  }

  .slick-icons--right {
    top: -4rem;
    right: 0rem;
  }

}

/* Add the following custom animations in your global styles (Tailwind CSS config or a custom file) */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Product image slide animations */
@keyframes slideLeft {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideRight {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-left {
  animation: slideLeft 0.8s cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
}

.animate-slide-right {
  animation: slideRight 0.8s cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
}

@keyframes shake {
  0% {
    transform: rotate(-5deg);
  }
  25% {
    transform: rotate(5deg);
  }
  50% {
    transform: rotate(-5deg);
  }
  75% {
    transform: rotate(5deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

.animate-fadeIn {
  animation: fadeIn 1.2s forwards;
}

.animate-shake {
  animation: shake 0.8s ease-in-out infinite;
}

@keyframes fadeSlideUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.animate-fade-slide-up {
  animation: fadeSlideUp 1s ease-out forwards;
}

.announcement-bar {
  display: none;
  background-color: var(--foreground);
  width: 100%;
  color: white;
  font-size: 0.875rem;
  align-items: center;
  justify-content: center;
  height: 2rem;
  padding: 0 1rem;
  overflow: hidden;
}

@media (min-width: 768px) {
  .announcement-bar {
    display: flex;
  }
}

.message-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 96rem;
  margin: 0 auto;
}

@keyframes scroll {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(-100%);
    opacity: 0;
  }
}

.animate-scroll {
  position: absolute;
  animation: scroll 11s linear infinite;
  white-space: nowrap;
}



