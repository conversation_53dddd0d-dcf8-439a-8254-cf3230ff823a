/* Masonry Grid Styles */
.masonry-grid {
  display: block;
}

.masonry-item {
  break-inside: avoid;
  position: relative;
}

/* Animation for masonry items */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.masonry-item {
  animation: fadeInUp 0.5s ease-out forwards;
  animation-play-state: paused;
}

.masonry-item.in-view {
  animation-play-state: running;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .masonry-grid {
    column-count: 1;
    margin-top: -10px; /* Negative margin to pull content up */
    padding-top: 0; /* Remove padding */
  }

  .masonry-item {
    margin-bottom: 16px; /* Standard spacing between items on mobile */
  }

  /* Standard animation for mobile */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Standard animation duration */
  .masonry-item {
    animation-duration: 0.5s;
  }
}

@media (min-width: 641px) and (max-width: 1023px) {
  .masonry-grid {
    column-count: 2;
  }
}

@media (min-width: 1024px) {
  .masonry-grid {
    column-count: 3;
  }
}

/* Hover effects for category cards */
.category-card-hover-effect:hover .category-name {
  transform: translateX(8px);
}

.category-card-hover-effect:hover .category-image {
  transform: scale(1.1);
}

.category-card-hover-effect:hover .category-overlay {
  opacity: 0.9;
}

.category-card-hover-effect:hover .category-description {
  opacity: 1;
  transform: translateY(0);
}

.category-card-hover-effect:hover .category-button {
  transform: translateX(4px);
}

.category-card-hover-effect:hover .category-line {
  width: 100%;
}

/* Instagram-like Video Modal Styles */
.video-modal-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.95);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.video-modal-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.video-modal-video {
  max-height: 80vh;
  max-width: 100%;
  object-fit: contain;
}

.video-modal-info {
  background-color: white;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
  padding: 16px;
  transform: translateY(0);
  transition: transform 0.3s ease;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

.video-modal-info.expanded {
  transform: translateY(-50%);
}

/* Floating add to cart button animation */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(236, 0, 63, 0.7);
    transform: scale(1);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(236, 0, 63, 0);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(236, 0, 63, 0);
    transform: scale(1);
  }
}

.video-modal-content button {
  animation: pulse 2s infinite;
}

/* Watch and Buy Mobile Card Styles */
.watch-buy-mobile-card {
  aspect-ratio: 9/16;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Watch and Buy Slider Styles */
.watch-buy-slider {
  margin: 0;
  padding: 0;
}

.watch-buy-slider .slick-track {
  display: flex;
  gap: 0;
}

.watch-buy-slider .slick-slide {
  height: auto;
  padding: 0;
  margin: 0;
}

.watch-buy-slider .slick-list {
  margin: 0;
}

.watch-buy-slider .slick-dots {
  margin-top: 0;
  bottom: -15px;
}

/* Custom spacing for cards */
.watch-buy-slider .slick-slide > div {
  margin: 0;
  padding: 0;
}

/* Fix width for different screen sizes */
@media (min-width: 1280px) {
  .watch-buy-slider .slick-slide {
    width: 20% !important;
  }
}

@media (min-width: 768px) and (max-width: 1279px) {
  .watch-buy-slider .slick-slide {
    width: 25% !important;
  }
}

/* Mobile video stacker styles */
@media (max-width: 767px) {
  .video-stacker-container {
    touch-action: none; /* Disable browser's default touch actions */
    height: 100vh !important;
    width: 100vw !important;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
  }

  .video-stack-wrapper {
    height: 100vh;
    width: 100%;
    overflow: hidden;
    touch-action: pan-y;
    -webkit-overflow-scrolling: touch;
    position: relative;
  }

  /* Enhanced YouTube Shorts-like transitions */
  .mobile-video-slide {
    position: absolute;
    width: 100%;
    height: 100%;
    transition: none; /* No transition by default - we control this with JS */
    will-change: transform; /* Optimize for animations */
  }

  .mobile-video-slide.active {
    transform: translateY(0);
    z-index: 10;
  }

  .mobile-video-slide.prev {
    transform: translateY(-100%); /* Position previous card fully above */
    z-index: 9;
    visibility: visible; /* Ensure it's visible when needed */
  }

  .mobile-video-slide.next {
    transform: translateY(100%); /* Position next card fully below */
    z-index: 9;
    visibility: visible; /* Ensure it's visible when needed */
  }

  /* Fixed YouTube Shorts-like swiping with precise finger control - one card at a time */
  .swiping-up .mobile-video-slide.active {
    transform: translateY(calc(var(--swipe-progress) * -100%)); /* Move UP (negative) when swiping up */
    transition: none; /* No transition during active swiping for direct control */
  }

  .swiping-up .mobile-video-slide.next {
    transform: translateY(calc(100% + (var(--swipe-progress) * -100%))); /* Next card stays at bottom until current card moves away */
    transition: none; /* No transition during active swiping for direct control */
  }

  .swiping-down .mobile-video-slide.active {
    transform: translateY(calc(var(--swipe-progress) * 100%)); /* Move DOWN (positive) when swiping down */
    transition: none; /* No transition during active swiping for direct control */
  }

  .swiping-down .mobile-video-slide.prev {
    transform: translateY(calc(-100% + (var(--swipe-progress) * 100%))); /* Prev card stays at top until current card moves away */
    transition: none; /* No transition during active swiping for direct control */
  }

  /* Active swiping state - completely removes transitions for direct finger control */
  .active-swiping .mobile-video-slide {
    transition: none !important;
  }

  /* Transition only applied when releasing finger - shorter duration for smoother effect */
  .transitioning .mobile-video-slide {
    transition: transform 0.25s cubic-bezier(0.16, 1, 0.3, 1); /* Spring-like transition with custom easing */
  }

  /* Special handling for the active card during transition to prevent jumps */
  .transitioning .mobile-video-slide.active {
    transition: transform 0.25s cubic-bezier(0.16, 1, 0.3, 1);
  }

  /* Manual transition classes for direct DOM manipulation */
  .mobile-video-slide.manual-transition {
    transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
  }

  .mobile-video-slide.moving-up {
    transform: translateY(-100%) !important;
  }

  .mobile-video-slide.moving-down {
    transform: translateY(100%) !important;
  }

  .mobile-video-slide.moving-to-center {
    transform: translateY(0) !important;
  }

  /* Ensure video controls are always on top */
  .video-controls-top {
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 50;
    display: flex;
    gap: 8px;
  }

  /* Enhanced swipe indicators with YouTube Shorts-like feedback */
  .swipe-indicator {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 4px;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 10px;
    z-index: 20;
    animation: pulse-opacity 2s infinite;
    transition: opacity 0.3s, transform 0.3s, width 0.3s;
  }

  /* Indicator animation */
  @keyframes pulse-opacity {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 0.8; }
  }

  /* Indicator positions */
  .swipe-indicator.top {
    top: 20px;
  }

  .swipe-indicator.bottom {
    bottom: 90px;
  }

  /* Active swiping indicator states */
  .active-swiping .swipe-indicator {
    width: 50px;
    opacity: 0.8;
  }

  .swiping-up .swipe-indicator.top {
    opacity: 0.9;
    transform: translateX(-50%) scaleX(1.2);
  }

  .swiping-down .swipe-indicator.bottom {
    opacity: 0.9;
    transform: translateX(-50%) scaleX(1.2);
  }

  /* Full width video */
  .mobile-video-slide .video-card,
  .mobile-video-card {
    width: 100vw !important;
    height: 100vh !important;
    max-width: none;
    max-height: none;
    position: absolute;
    top: 0;
    left: 0;
  }

  .mobile-video-slide .react-player,
  .mobile-video-card .react-player {
    width: 100vw !important;
    height: 100vh !important;
    max-width: none;
    max-height: none;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
  }

  /* Make sure controls and product info are always visible */
  .mobile-video-slide .absolute,
  .mobile-video-card .absolute {
    z-index: 20;
  }

  /* Product info overlay styling */
  .mobile-video-slide .bg-gradient-to-t,
  .mobile-video-card .bg-gradient-to-t {
    z-index: 30;
    padding-bottom: 20px; /* Extra padding at bottom for better visibility */
  }

  /* Video controls styling */
  .video-controls {
    position: absolute;
    top: 16px;
    right: 16px;
    display: flex;
    gap: 8px;
    z-index: 40;
  }

  .video-control-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .video-control-button:hover {
    background-color: rgba(0, 0, 0, 0.5);
  }
}

.watch-buy-mobile-card .play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 48px;
  height: 48px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.watch-buy-mobile-card .play-button::after {
  content: '';
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-left: 16px solid black;
  border-bottom: 10px solid transparent;
  margin-left: 4px;
}

/* Slider customizations for Watch and Buy section */
.watch-buy-slider .slick-dots {
  bottom: -30px;
}

.watch-buy-slider .slick-dots li button:before {
  font-size: 8px;
  color: #888;
}

.watch-buy-slider .slick-dots li.slick-active button:before {
  color: #000;
}

.watch-buy-slider .slick-slide {
  padding: 0 8px;
}

/* Desktop Watch and Buy enhancements */
@media (min-width: 768px) {
  /* Card hover effects */
  .group:hover .group-hover\:scale-105 {
    transform: scale(1.05);
  }

  /* Smooth transitions */
  .transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  /* Card active state */
  [aria-current="true"] .bg-black\/80 {
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
  }

  /* Like button animation */
  @keyframes heartBeat {
    0% {
      transform: scale(1);
    }
    14% {
      transform: scale(1.3);
    }
    28% {
      transform: scale(1);
    }
    42% {
      transform: scale(1.3);
    }
    70% {
      transform: scale(1);
    }
  }

  .fill-current.scale-110 {
    animation: heartBeat 1.3s ease-in-out;
  }

  /* VideoStacker UI Styles */
  .video-stacker-container {
    border-radius: 12px;
    overflow: hidden;
  }

  .video-stack-wrapper {
    perspective: 1000px;
  }

  .video-card {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  /* FastMovingCard video modal styles */
  .fast-moving-video-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    background-color: #000;
    display: flex;
    flex-direction: column;
    width: 100vw;
    height: 100vh;
    height: -webkit-fill-available;
    height: -moz-available;
    height: fill-available;
    overflow: hidden;
    touch-action: manipulation;
    -webkit-overflow-scrolling: touch;
  }

  .fast-moving-video-modal video {
    width: 100vw !important;
    height: 100vh !important;
    object-fit: cover;
    object-position: center;
    display: block;
    background-color: #000;
  }

  /* Ensure safe area handling for notched devices */
  .safe-area-inset-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-inset-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Fix for iOS Safari viewport issues */
  @supports (-webkit-touch-callout: none) {
    .fast-moving-video-modal {
      height: -webkit-fill-available;
    }
  }

  /* Prevent body scroll when modal is open */
  body.modal-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
    height: 100%;
  }

  /* Mobile-specific video rendering fixes */
  @media (max-width: 768px) {
    .fast-moving-video-modal {
      /* Ensure modal takes full viewport on mobile */
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 10000;
      background: #000;
      display: block;
    }

    .fast-moving-video-modal video {
      /* Force video to take full mobile viewport */
      position: absolute;
      top: 0;
      left: 0;
      width: 100vw !important;
      height: 100vh !important;
      object-fit: cover;
      object-position: center;
      z-index: 1;
    }

    .fast-moving-video-modal .absolute {
      /* Ensure overlays appear above video */
      z-index: 10;
    }

    /* Fix for mobile browsers with dynamic viewport */
    .fast-moving-video-modal {
      height: 100dvh; /* Dynamic viewport height */
      min-height: 100vh;
      min-height: -webkit-fill-available;
    }
  }

  /* Additional fixes for iOS Safari */
  @supports (-webkit-appearance: none) {
    .fast-moving-video-modal {
      height: -webkit-fill-available;
    }
  }

  /* Video card hover effect */
  .video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.6);
  }

  /* Animation for video card transitions */
  @keyframes slideInFromRight {
    0% {
      transform: translateX(100%) scale(0.9);
      opacity: 0.7;
    }
    100% {
      transform: translateX(60%) scale(0.9);
      opacity: 0.7;
    }
  }

  @keyframes slideInFromLeft {
    0% {
      transform: translateX(-100%) scale(0.9);
      opacity: 0.7;
    }
    100% {
      transform: translateX(-60%) scale(0.9);
      opacity: 0.7;
    }
  }

  @keyframes slideToCenter {
    0% {
      transform: translateX(60%) scale(0.9);
      opacity: 0.7;
    }
    100% {
      transform: translateX(0) scale(1);
      opacity: 1;
    }
  }

  /* React Player styling */
  .react-player {
    position: absolute;
    top: 0;
    left: 0;
  }

  /* Timeline indicators */
  .video-timeline-indicator {
    transition: all 0.3s ease;
  }

  .video-timeline-indicator:hover {
    transform: scaleY(1.5);
  }
}

  /* Add to cart button hover effect */
  .hover\:scale-105:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

/* Add styles for video controls */
.video-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 15px;
  z-index: 30;
}

.video-control-button {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  backdrop-filter: blur(4px);
}
