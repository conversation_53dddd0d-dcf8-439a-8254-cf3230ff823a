import React, { useState, useEffect, useRef, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import Slider from "react-slick";
import { X, ShoppingBag, Play, Pause, Volume2, VolumeX } from "lucide-react";
import FastMovingCard from "./fast-moving-card";
import "@/styles/watch-and-buy-mobile.css";

// Cloudinary Video Player Component for Mobile
function VideoPlayer({ videoUrl, isPlaying, isMuted, onProgress, onEnded, onError, className, style, autoplay = false }) {
  const videoRef = useRef();
  const playerInstanceRef = useRef();
  const containerRef = useRef();
  const [isCloudinaryLoaded, setIsCloudinaryLoaded] = useState(false);
  const [isPlayerReady, setIsPlayerReady] = useState(false);

  // Extract public ID from Cloudinary URL
  const extractPublicId = (url) => {
    if (!url || !url.includes('cloudinary.com')) {
      console.log('Not a Cloudinary URL, returning as-is:', url);
      return url;
    }

    try {
      console.log('Processing Cloudinary URL:', url);

      // For URL like: https://res.cloudinary.com/dkqt39aad/video/upload/q_auto/f_auto/v1748080301/cpc8j2kmwsvkexprtoyh.mp4
      // We need to extract: cpc8j2kmwsvkexprtoyh

      // Find the upload part and get everything after it
      const uploadIndex = url.indexOf('/upload/');
      if (uploadIndex === -1) {
        console.log('No /upload/ found in URL');
        return url;
      }

      // Get the part after /upload/
      const afterUpload = url.substring(uploadIndex + 8); // 8 = length of '/upload/'
      console.log('After upload part:', afterUpload);

      // Split by '/' to get segments
      const segments = afterUpload.split('/');
      console.log('URL segments:', segments);

      // Find the last segment (which contains the public ID + extension)
      const lastSegment = segments[segments.length - 1];
      console.log('Last segment:', lastSegment);

      // Remove file extension (.mp4, .mov, etc.)
      const publicId = lastSegment.replace(/\.[^/.]+$/, '');

      console.log('Final extracted public ID:', publicId, 'from URL:', url);
      return publicId;
    } catch (error) {
      console.error('Error extracting public ID:', error);
      return url;
    }
  };

  // Check if Cloudinary is loaded
  useEffect(() => {
    const checkCloudinary = () => {
      if (window.cloudinary) {
        setIsCloudinaryLoaded(true);
        return true;
      }
      return false;
    };

    if (checkCloudinary()) {
      return;
    }

    // Poll for Cloudinary to be loaded
    const interval = setInterval(() => {
      if (checkCloudinary()) {
        clearInterval(interval);
      }
    }, 100);

    return () => clearInterval(interval);
  }, []);

  // Initialize Cloudinary player
  useEffect(() => {
    if (!isCloudinaryLoaded || playerInstanceRef.current || !videoRef.current) return;

    try {
      console.log('Initializing Cloudinary player for mobile:', videoUrl);

      // Create a timeout to ensure DOM is ready
      const initTimeout = setTimeout(() => {
        if (!videoRef.current) {
          console.warn('Video element not available for initialization');
          return;
        }

        // Ensure video element is properly set up
        const videoElement = videoRef.current;
        videoElement.preload = 'metadata';
        videoElement.playsInline = true;
        videoElement.muted = true;

        playerInstanceRef.current = window.cloudinary.videoPlayer(videoElement, {
          cloud_name: 'dkqt39aad',
          controls: false,
          autoplay: autoplay,
          muted: true,
          loop: false,
          fluid: true,
          playsinline: true,
          preload: 'metadata',
          transformation: {
            quality: 'auto',
            fetch_format: 'auto'
          }
        });

        console.log('Cloudinary player initialized for mobile:', playerInstanceRef.current);
        setIsPlayerReady(true);

        // Set up event listeners
        if (playerInstanceRef.current) {
          // Use a more robust approach for progress tracking
          playerInstanceRef.current.on('timeupdate', () => {
            if (onProgress && playerInstanceRef.current) {
              try {
                const player = playerInstanceRef.current;
                const currentTime = player.currentTime();
                const duration = player.duration();

                if (duration && duration > 0 && !isNaN(currentTime) && !isNaN(duration)) {
                  const progress = currentTime / duration;
                  onProgress({ played: progress });
                }
              } catch (err) {
                console.warn('Error in timeupdate:', err);
              }
            }
          });

          playerInstanceRef.current.on('ended', () => {
            if (onEnded) onEnded();
          });

          playerInstanceRef.current.on('error', (error) => {
            console.error('Cloudinary player error:', error);
            setIsPlayerReady(false);
            if (onError) onError(error);
          });

          playerInstanceRef.current.on('loadstart', () => {
            console.log('Video load started');
          });

          playerInstanceRef.current.on('loadeddata', () => {
            console.log('Video data loaded');
          });

          playerInstanceRef.current.on('loadedmetadata', () => {
            console.log('Video metadata loaded');
            setIsPlayerReady(true);
          });

          playerInstanceRef.current.on('canplay', () => {
            console.log('Video can start playing');
          });

          playerInstanceRef.current.on('playing', () => {
            console.log('Video is playing');
          });

          playerInstanceRef.current.on('pause', () => {
            console.log('Video is paused');
          });

          playerInstanceRef.current.on('ready', () => {
            console.log('Cloudinary player ready');
            setIsPlayerReady(true);

            // Set video source immediately when player is ready
            if (videoUrl) {
              try {
                const publicId = extractPublicId(videoUrl);
                console.log('Setting video source on ready with public ID:', publicId);

                playerInstanceRef.current.source(publicId, {
                  transformation: {
                    quality: 'auto',
                    fetch_format: 'auto'
                  }
                });

                console.log('Video source set successfully');

                // If autoplay is enabled, start playing immediately
                if (autoplay) {
                  console.log('Autoplay enabled, starting video');
                  setTimeout(() => {
                    if (playerInstanceRef.current) {
                      try {
                        const playPromise = playerInstanceRef.current.play();
                        if (playPromise !== undefined) {
                          playPromise.then(() => {
                            console.log('Autoplay started successfully');
                          }).catch(error => {
                            console.warn('Autoplay failed:', error);
                          });
                        }
                      } catch (playError) {
                        console.warn('Error starting autoplay:', playError);
                      }
                    }
                  }, 1000); // Increased delay to ensure video is fully loaded
                }
              } catch (sourceError) {
                console.error('Error setting video source on ready:', sourceError);
              }
            }
          });
        }
      }, 100);

      return () => clearTimeout(initTimeout);
    } catch (error) {
      console.error('Error initializing Cloudinary player:', error);
      if (onError) onError(error);
    }
  }, [isCloudinaryLoaded, videoUrl, onProgress, onEnded, onError, autoplay]);

  // Handle play/pause
  useEffect(() => {
    if (playerInstanceRef.current && isPlayerReady) {
      try {
        if (isPlaying) {
          const playPromise = playerInstanceRef.current.play();
          if (playPromise !== undefined) {
            playPromise.catch(error => {
              console.warn('Play failed:', error);
            });
          }
        } else {
          playerInstanceRef.current.pause();
        }
      } catch (error) {
        console.error('Error controlling playback:', error);
      }
    }
  }, [isPlaying, isPlayerReady]);

  // Handle mute/unmute
  useEffect(() => {
    if (playerInstanceRef.current) {
      try {
        if (isMuted) {
          playerInstanceRef.current.mute();
        } else {
          playerInstanceRef.current.unmute();
        }
      } catch (error) {
        console.error('Error controlling volume:', error);
      }
    }
  }, [isMuted]);

  // Update video source when videoUrl changes
  useEffect(() => {
    if (playerInstanceRef.current && videoUrl) {
      try {
        const publicId = extractPublicId(videoUrl);
        console.log('Setting video source with public ID:', publicId);

        playerInstanceRef.current.source(publicId, {
          transformation: {
            quality: 'auto',
            fetch_format: 'auto'
          }
        });
      } catch (error) {
        console.error('Error setting video source:', error);
        if (onError) onError(error);
      }
    }
  }, [videoUrl]);

  // Cleanup function
  useEffect(() => {
    return () => {
      if (playerInstanceRef.current) {
        try {
          console.log('Cleaning up Cloudinary player');

          // Pause the video first
          if (typeof playerInstanceRef.current.pause === 'function') {
            playerInstanceRef.current.pause();
          }

          // Remove all event listeners
          if (typeof playerInstanceRef.current.off === 'function') {
            playerInstanceRef.current.off();
          }

          // Dispose the player
          if (typeof playerInstanceRef.current.dispose === 'function') {
            playerInstanceRef.current.dispose();
          }

          playerInstanceRef.current = null;
        } catch (error) {
          console.error('Error disposing Cloudinary player:', error);
          // Force cleanup
          playerInstanceRef.current = null;
        }
      }
    };
  }, []);

  return (
    <div ref={containerRef} className={className} style={style}>
      <video
        ref={videoRef}
        style={{ width: '100%', height: '100%' }}
        playsInline
        preload="metadata"
        muted
        onContextMenu={(e) => e.preventDefault()}
        suppressHydrationWarning={true}
        onLoadStart={() => console.log('Video element load started')}
        onLoadedMetadata={() => console.log('Video element metadata loaded')}
        onCanPlay={() => console.log('Video element can play')}
        onError={(e) => console.error('Video element error:', e)}
      />
      {!isPlayerReady && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/20">
          <div className="text-white text-sm">Loading video...</div>
        </div>
      )}
    </div>
  );
}

const WatchAndBuyMobile = ({ products, handleAddtoCart }) => {
  const navigate = useNavigate();
  const [showVideoModal, setShowVideoModal] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [isPlaying, setIsPlaying] = useState(true);
  const [isMuted, setIsMuted] = useState(false);
  const [videoProgress, setVideoProgress] = useState(0);
  const videoContainerRef = useRef(null);
  const progressBarRef = useRef(null);
  const sliderRef = useRef(null);

  // Function to go to the next video (infinite loop)
  const goToNextVideo = useCallback(() => {
    const newIndex = (currentVideoIndex + 1) % products.length;
    setCurrentVideoIndex(newIndex);
    setSelectedVideo(products[newIndex]);
  }, [currentVideoIndex, products]);

  // Function to go to the previous video (infinite loop)
  const goToPrevVideo = useCallback(() => {
    const newIndex = currentVideoIndex === 0 ? products.length - 1 : currentVideoIndex - 1;
    setCurrentVideoIndex(newIndex);
    setSelectedVideo(products[newIndex]);
  }, [currentVideoIndex, products]);

  // Handle video end - auto slide to next video with a slight delay
  const handleVideoEnd = useCallback(() => {
    // Simple delay before going to next video
    setTimeout(() => {
      goToNextVideo();
    }, 300);
  }, [goToNextVideo]);

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Handle touch events for mobile video swiping
  const handleTouchStart = (e) => {
    if (!isMobile || !showVideoModal) return;

    // Get all the video slides
    const slides = document.querySelectorAll('.mobile-video-slide');
    if (!slides.length) return;

    // Store the starting touch position
    const touchY = e.touches[0].clientY;
    videoContainerRef.current.dataset.touchStartY = touchY;

    // Add a class to indicate active swiping
    videoContainerRef.current.classList.add('active-swiping');
  };

  const handleTouchMove = (e) => {
    if (!isMobile || !showVideoModal) return;
    // Removed e.preventDefault() as we're using touchAction: 'none' instead

    const slides = document.querySelectorAll('.mobile-video-slide');
    if (!slides.length) return;

    // Calculate how far we've swiped
    const touchStartY = parseFloat(videoContainerRef.current.dataset.touchStartY);
    const touchY = e.touches[0].clientY;
    const diff = touchStartY - touchY;

    // Find the active slide and the target slide (next or previous)
    let activeSlide = null;
    let targetSlide = null;

    slides.forEach(slide => {
      if (slide.classList.contains('active')) {
        activeSlide = slide;
      }
    });

    if (!activeSlide) return;

    // Determine swipe direction
    const direction = diff > 0 ? 'up' : 'down';

    // Find the target slide based on direction
    if (direction === 'up') {
      slides.forEach(slide => {
        if (slide.classList.contains('next')) {
          targetSlide = slide;
        }
      });
    } else {
      slides.forEach(slide => {
        if (slide.classList.contains('prev')) {
          targetSlide = slide;
        }
      });
    }

    if (!targetSlide) return;

    // Calculate the percentage of the swipe (0 to 1)
    const screenHeight = window.innerHeight;
    const swipePercentage = Math.min(Math.abs(diff) / (screenHeight * 0.3), 1);

    // Apply transforms based on swipe direction and percentage
    if (direction === 'up') {
      activeSlide.style.transform = `translateY(${-swipePercentage * 100}%)`;
      targetSlide.style.transform = `translateY(${(100 + 20/screenHeight*100) - swipePercentage * (100 + 20/screenHeight*100)}%)`;
    } else {
      activeSlide.style.transform = `translateY(${swipePercentage * 100}%)`;
      targetSlide.style.transform = `translateY(${-(100 + 20/screenHeight*100) + swipePercentage * (100 + 20/screenHeight*100)}%)`;
    }
  };

  const handleTouchEnd = (e) => {
    if (!isMobile || !showVideoModal) return;

    // Remove active swiping class
    videoContainerRef.current.classList.remove('active-swiping');

    const slides = document.querySelectorAll('.mobile-video-slide');
    if (!slides.length) return;

    // Calculate how far we've swiped
    const touchStartY = parseFloat(videoContainerRef.current.dataset.touchStartY);
    const touchY = e.changedTouches[0].clientY;
    const diff = touchStartY - touchY;

    // Find the active slide and the target slide (next or previous)
    let activeSlide = null;
    let targetSlide = null;

    slides.forEach(slide => {
      if (slide.classList.contains('active')) {
        activeSlide = slide;
      }
    });

    if (!activeSlide) return;

    // Determine swipe direction
    const direction = diff > 0 ? 'up' : 'down';

    // Find the target slide based on direction
    if (direction === 'up') {
      slides.forEach(slide => {
        if (slide.classList.contains('next')) {
          targetSlide = slide;
        }
      });
    } else {
      slides.forEach(slide => {
        if (slide.classList.contains('prev')) {
          targetSlide = slide;
        }
      });
    }

    if (!targetSlide) return;

    // Determine if the swipe was far enough to change slides
    const threshold = window.innerHeight * 0.15;
    const shouldChangeSlide = Math.abs(diff) > threshold;

    // Add transition class for smooth animation
    activeSlide.classList.add('manual-transition');
    targetSlide.classList.add('manual-transition');

    if (shouldChangeSlide) {
      // Complete the transition
      if (direction === 'up') {
        // Move to next slide
        activeSlide.style.transform = 'translateY(-100%)';
        targetSlide.style.transform = 'translateY(0)';

        // After animation completes, update the current index
        setTimeout(() => {
          const newIndex = (currentVideoIndex + 1) % products.length;
          setCurrentVideoIndex(newIndex);
          setSelectedVideo(products[newIndex]);

          // Reset all slides
          slides.forEach(slide => {
            slide.classList.remove('manual-transition');
            slide.style.transform = '';
          });
        }, 300);
      } else {
        // Move to previous slide
        activeSlide.style.transform = 'translateY(100%)';
        targetSlide.style.transform = 'translateY(0)';

        // After animation completes, update the current index
        setTimeout(() => {
          const newIndex = currentVideoIndex === 0 ? products.length - 1 : currentVideoIndex - 1;
          setCurrentVideoIndex(newIndex);
          setSelectedVideo(products[newIndex]);

          // Reset all slides
          slides.forEach(slide => {
            slide.classList.remove('manual-transition');
            slide.style.transform = '';
          });
        }, 300);
      }
    } else {
      // Return to original positions
      activeSlide.style.transform = 'translateY(0)';
      if (direction === 'up') {
        targetSlide.style.transform = 'translateY(100%)';
      } else {
        targetSlide.style.transform = 'translateY(-100%)';
      }

      // After animation completes, remove transition class
      setTimeout(() => {
        slides.forEach(slide => {
          slide.classList.remove('manual-transition');
          slide.style.transform = '';
        });
      }, 300);
    }
  };

  // Settings for the slider
  const sliderSettings = {
    dots: false, // We're using custom dots
    infinite: true,
    speed: 500,
    slidesToShow: 5,
    slidesToScroll: 1,
    arrows: false,
    autoplay: true,
    autoplaySpeed: 3000,
    swipeToSlide: true,
    beforeChange: (_, next) => setCurrentSlideIndex(next),
    responsive: [
      {
        breakpoint: 1280,
        settings: {
          slidesToShow: 4,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 640,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
          arrows: false,
        },
      },
    ],
  };

  // Reset video state when modal opens or video changes
  useEffect(() => {
    if (showVideoModal) {
      setIsPlaying(true);
      setIsMuted(false);
      setVideoProgress(0);
    }
  }, [showVideoModal, currentVideoIndex]);

  // Check if we have products to display
  const hasWatchAndBuyProducts = products && products.length > 0;

  if (!hasWatchAndBuyProducts) return null;

  return (
    <section className="py-6 md:py-12 bg-white">
      <div className="container mx-auto px-2">
        <div className="max-w-3xl mx-auto text-center mb-8">
          <h2 className="text-3xl md:text-4xl font-light uppercase tracking-wide mb-4">Watch And Buy</h2>
          <div className="w-24 h-1 bg-black mx-auto mb-6"></div>
          <p className="text-gray-600">Explore our curated collection of trending products</p>
        </div>

        {/* Watch and Buy Slider - Both Mobile and Desktop */}
        <div className="w-full mb-4 px-1">
          <div>
            <Slider ref={sliderRef} {...sliderSettings} className="watch-buy-slider mobile-watch-buy-slider">
              {products.map((productItem, index) => (
                <div key={productItem._id} className="pb-2">
                  <div
                    onClick={() => {
                      setSelectedVideo(productItem);
                      setCurrentVideoIndex(index);
                      setShowVideoModal(true);
                    }}
                    className="relative"
                  >
                    <div
                      className="relative cursor-pointer shadow-md overflow-hidden watch-buy-mobile-card"
                      style={{
                        aspectRatio: "8/16",
                        background: "#f8f8f8",
                      }}
                    >
                      {/* Video thumbnail with play button overlay */}
                      <div className="absolute inset-0 flex items-center justify-center z-10">
                        <div className="w-12 h-12 rounded-full bg-white bg-opacity-70 flex items-center justify-center">
                          <div className="w-0 h-0 border-t-[8px] border-t-transparent border-l-[14px] border-l-black border-b-[8px] border-b-transparent ml-1"></div>
                        </div>
                      </div>

                      <FastMovingCard
                        item={productItem}
                        index={index}
                        activeItem={0}
                        handleAddtoCart={handleAddtoCart}
                        isMobileCard={true}
                      />
                    </div>

                    {/* Product info below the card */}
                    <div className="mt-2 px-0">
                      <h3 className="text-sm font-medium line-clamp-1 mb-1">{productItem?.title}</h3>
                      <div className="flex justify-between items-center">
                        <p className="text-sm font-bold">₹{productItem.price}</p>
                        <div className="flex gap-2">
                        <button
                            onClick={(e) => {
                              e.stopPropagation();
                              navigate(`/shop/details/${productItem._id}`);
                            }}
                            className="w-8 h-8 bg-white border border-gray-300 text-black rounded-full flex items-center justify-center hover:bg-gray-100 transition-colors shadow-sm"
                            aria-label="View Details"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                              <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                          </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAddtoCart(productItem);
                          }}
                          className="text-xs bg-black text-white p-1.5 rounded-full"
                          aria-label="Add to Cart"
                        >
                          <ShoppingBag className="h-4 w-5" />
                        </button>
                        </div>

                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </Slider>
          </div>
        </div>

        {/* Custom Dots Indicator */}
        <div className="custom-dots">
          {products.slice(0, Math.min(products.length, 10)).map((_, index) => (
            <button
              key={index}
              onClick={() => {
                // When using dots navigation, go directly to the selected slide
                if (sliderRef.current) {
                  sliderRef.current.slickGoTo(index);
                }
              }}
              className={`custom-dot ${index === currentSlideIndex ? 'active' : ''}`}
              aria-label={`Go to slide ${index + 1}`}
            ></button>
          ))}
        </div>
      </div>

      {/* VideoStacker Modal - When a video is clicked */}
      {showVideoModal && selectedVideo && (
        <div
          className="fixed inset-0 bg-black z-50 flex flex-col"
          onClick={() => setShowVideoModal(false)}
        >
          {/* Semi-transparent overlay at the top for better control visibility */}
          <div className="absolute top-0 left-0 right-0 h-24 bg-gradient-to-b from-black/80 to-transparent z-40 pointer-events-none"></div>

          {/* Removed bottom overlay as it's now part of the product info section */}

          {/* Modal Header - Controls in Top Right */}
          <div className="absolute top-0 left-0 right-0 p-4 flex justify-between items-center z-50">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowVideoModal(false);
              }}
              className="w-10 h-10 rounded-full bg-black/50 backdrop-blur-sm flex items-center justify-center hover:bg-black/70 transition-colors"
            >
              <X className="h-5 w-5 text-white" stroke="currentColor" />
            </button>

            {/* Video Controls in Top Right */}
            <div className="flex items-center gap-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setIsPlaying(!isPlaying);
                }}
                className="w-10 h-10 rounded-full bg-black/50 backdrop-blur-sm flex items-center justify-center hover:bg-black/70 transition-colors"
              >
                {isPlaying ? (
                  <Pause className="h-5 w-5 text-white" />
                ) : (
                  <Play className="h-5 w-5 text-white ml-0.5" />
                )}
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setIsMuted(!isMuted);
                }}
                className="w-10 h-10 rounded-full bg-black/50 backdrop-blur-sm flex items-center justify-center hover:bg-black/70 transition-colors"
              >
                {isMuted ? (
                  <VolumeX className="h-5 w-5 text-white" />
                ) : (
                  <Volume2 className="h-5 w-5 text-white" />
                )}
              </button>
            </div>
          </div>

          {/* Video Progress Bar at the top */}
          <div className="absolute top-0 left-0 right-0 z-40 h-1 bg-white/20">
            <div
              ref={progressBarRef}
              className="h-full bg-white transition-all duration-100 ease-linear"
              style={{ width: `${videoProgress * 100}%` }}
            ></div>
          </div>

          {/* VideoStacker UI - Full Height */}
          <div
            className="h-full w-full video-stacker-wrapper"
            ref={videoContainerRef}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            style={{ touchAction: 'none', height: '100vh', maxHeight: '-webkit-fill-available' }} /* This prevents browser default touch actions */
          >
            <div className="video-stacker-container relative h-full w-full overflow-hidden">
              {/* Stacked Videos */}
              <div className="video-stack-wrapper relative h-full w-full flex flex-col">
                {products.map((productItem, index) => {
                  // For mobile: Only show current, previous, and next videos
                  if (index !== currentVideoIndex &&
                      index !== (currentVideoIndex + 1) % products.length &&
                      index !== (currentVideoIndex - 1 + products.length) % products.length) {
                    return null; // Don't render videos that aren't visible in the carousel
                  }

                  return (
                    <div
                      key={productItem._id}
                      className={`mobile-video-slide ${
                         index === currentVideoIndex ? 'active' :
                         index === (currentVideoIndex + 1) % products.length ? 'next' :
                         index === (currentVideoIndex - 1 + products.length) % products.length ? 'prev' : ''
                        }`}
                    >
                      <div
                        className="video-card relative overflow-hidden mobile-video-card"
                        style={{ width: '100vw', height: '100vh', maxHeight: '-webkit-fill-available' }}
                      >
                        {/* Video Player - Full Height */}
                        {productItem.videoUrl || productItem.video ? (
                          <div className="w-full h-full">
                            <div
                              className="react-player-container h-full w-full"
                              onContextMenu={(e) => e.preventDefault()}
                            >
                              <VideoPlayer
                                key={`${productItem._id}-${index}-${showVideoModal}`}
                                videoUrl={productItem.videoUrl || productItem.video}
                                className="react-player"
                                style={{ width: "100%", height: "100%" }}
                                isPlaying={index === currentVideoIndex ? isPlaying : false}
                                isMuted={isMuted}
                                autoplay={index === currentVideoIndex && showVideoModal}
                                onProgress={(state) => index === currentVideoIndex && setVideoProgress(state.played)}
                                onEnded={index === currentVideoIndex ? handleVideoEnd : undefined}
                                onError={(e) => console.log("Video error:", e)}
                              />
                            </div>
                          </div>
                        ) : (
                          <div className="w-full h-full bg-gray-800 flex items-center justify-center">
                            <p className="text-white">Video not available</p>
                          </div>
                        )}

                        {/* Product Info Overlay - Improved positioning and visibility */}
                        <div className="absolute bottom-0 left-0 right-0 px-4 pb-8 z-40 bg-gradient-to-t from-black/90 via-black/70 to-transparent pt-16">
                          <div className="flex flex-col text-white">
                            <h3 className="text-xl font-medium truncate text-white drop-shadow-md">{productItem?.title}</h3>
                            <p className="text-xl font-bold mt-2 text-white drop-shadow-md">₹{productItem.price}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Global Floating Action Buttons */}
          <div className="absolute bottom-6 right-6 flex flex-col gap-3 z-50">
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleAddtoCart(selectedVideo);
              }}
              className="w-12 h-12 rounded-full bg-black text-white flex items-center justify-center shadow-xl hover:bg-black/80 transition-colors border border-white/20"
              aria-label="Add to Cart"
            >
              <ShoppingBag className="h-5 w-5" />
            </button>

            <button
              onClick={(e) => {
                e.stopPropagation();
                navigate(`/shop/details/${selectedVideo._id}`);
                setShowVideoModal(false);
              }}
              className="w-12 h-12 rounded-full bg-white text-black flex items-center justify-center shadow-xl hover:bg-gray-100 transition-colors"
              aria-label="View Details"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path><circle cx="12" cy="12" r="3"></circle></svg>
            </button>
          </div>
        </div>
      )}
    </section>
  );
};

export default WatchAndBuyMobile;
