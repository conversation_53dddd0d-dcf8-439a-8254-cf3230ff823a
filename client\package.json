{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run generate-sitemap && vite build && npm run copy-sitemap", "copy-sitemap": "node -e \"const fs = require('fs'); if (fs.existsSync('./public/sitemap.xml')) { fs.copyFileSync('./public/sitemap.xml', './dist/sitemap.xml'); console.log('Sitemap copied to dist directory'); } if (fs.existsSync('./public/robots.txt')) { fs.copyFileSync('./public/robots.txt', './dist/robots.txt'); console.log('Robots.txt copied to dist directory'); } if (fs.existsSync('./public/404.html')) { fs.copyFileSync('./public/404.html', './dist/404.html'); console.log('404.html copied to dist directory'); } if (fs.existsSync('./public/_redirects')) { fs.copyFileSync('./public/_redirects', './dist/_redirects'); console.log('_redirects copied to dist directory'); }\"", "lint": "eslint .", "preview": "vite preview", "generate-sitemap": "node scripts/generate-sitemap.cjs", "check-sitemap": "node scripts/check-sitemap.cjs"}, "dependencies": {"@cloudinary/react": "^1.14.3", "@cloudinary/url-gen": "^1.21.0", "@ffmpeg/core": "^0.12.10", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@reduxjs/toolkit": "^2.2.7", "axios": "^1.7.4", "chart.js": "^4.4.7", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "cloudinary-react": "^1.8.1", "clsx": "^2.1.1", "framer-motion": "^12.0.5", "hls.js": "^1.6.1", "install": "^0.13.0", "lucide-react": "^0.429.0", "npm": "^11.0.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-player": "^2.16.0", "react-redux": "^9.1.2", "react-router-dom": "^6.26.1", "react-slick": "^0.30.3", "react-social-media-embed": "^2.5.17", "react-zoom-pan-pinch": "^3.7.0", "sitemap": "^8.0.0", "slick-carousel": "^1.8.1", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vite-plugin-checker": "^0.8.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-pwa": "^0.21.1"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.41", "tailwindcss": "^3.4.10", "vite": "^5.4.1"}}