/* Custom styles for the watch-and-buy-mobile component */

/* Mobile slider specific styles */
.mobile-watch-buy-slider .slick-track {
  display: flex;
  gap: 12px;
}

.mobile-watch-buy-slider .slick-slide {
  padding: 0px;
}

/* Ensure the video cards have proper styling */
.watch-buy-mobile-card {
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  width: 100%;
}

.watch-buy-mobile-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Mobile video slide transitions */
.mobile-video-slide {
  position: absolute;
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
  display: flex;
  justify-content: center;
  /* Remove padding to allow full-height display */
  padding: 0;
  overflow: hidden;
}

.mobile-video-slide.active {
  transform: translateY(0);
  z-index: 20;
}

.mobile-video-slide.next {
  transform: translateY(calc(100% + 20px));
  z-index: 10;
}

.mobile-video-slide.prev {
  transform: translateY(calc(-100% - 20px));
  z-index: 10;
}

.mobile-video-slide.manual-transition {
  transition: transform 0.3s ease;
}

/* Styling for video cards */
.mobile-video-card {
  overflow: hidden;
}

/* Only add rounded corners to cards in the slider, not in the modal */
.watch-buy-slider .mobile-video-card {
  border-radius: 12px;
}

/* Ensure controls are always visible */
.video-controls-container {
  z-index: 100;
}

/* Prevent context menu and selection on video player */
.react-player-container {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  pointer-events: auto;
  position: relative;
  background-color: #000;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.react-player-container video {
  pointer-events: auto;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  object-fit: cover; /* Changed from contain to cover to fill the entire space */
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

/* Ensure ReactPlayer fills the container properly */
.react-player {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
}

/* Custom dots styling */
.custom-dots {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  margin-bottom: 2rem;
}

.custom-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin: 0 4px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.custom-dot.active {
  background-color: #000;
  border: 1px solid #ccc;
  transform: scale(1.25);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.custom-dot:not(.active) {
  background-color: #aaa;
}

.custom-dot:not(.active):hover {
  background-color: #666;
}

/* VideoStacker wrapper styles for full height */
.video-stacker-wrapper {
  height: 100vh;
  height: -webkit-fill-available;
  height: -moz-available;
  height: fill-available;
  display: flex;
  flex-direction: column;
}

/* Ensure product info text is always visible with good contrast */
.mobile-video-slide h3,
.mobile-video-slide p {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
  letter-spacing: 0.01em;
}

/* Floating action buttons animation */
button[aria-label="Add to Cart"],
button[aria-label="View Details"] {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

button[aria-label="Add to Cart"]:hover,
button[aria-label="View Details"]:hover {
  transform: scale(1.1);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

button[aria-label="Add to Cart"]:active,
button[aria-label="View Details"]:active {
  transform: scale(0.95);
}
